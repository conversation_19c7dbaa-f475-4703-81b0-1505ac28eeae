/*! @azure/msal-browser v4.12.0 2025-05-06 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
// Status Codes that can be thrown by WAM
const USER_INTERACTION_REQUIRED = "USER_INTERACTION_REQUIRED";
const USER_CANCEL = "USER_CANCEL";
const NO_NETWORK = "NO_NETWORK";
const PERSISTENT_ERROR = "PERSISTENT_ERROR";
const DISABLED = "DISABLED";
const ACCOUNT_UNAVAILABLE = "ACCOUNT_UNAVAILABLE";

export { ACCOUNT_UNAVAILABLE, DISABLED, NO_NETWORK, PERSISTENT_ERROR, USER_CANCEL, USER_INTERACTION_REQUIRED };
//# sourceMappingURL=NativeStatusCodes.mjs.map
