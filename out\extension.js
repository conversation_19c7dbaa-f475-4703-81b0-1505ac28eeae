(()=>{"use strict";var e={265:function(e,t,o){var n=this&&this.__createBinding||(Object.create?function(e,t,o,n){void 0===n&&(n=o);var i=Object.getOwnPropertyDescriptor(t,o);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[o]}}),Object.defineProperty(e,n,i)}:function(e,t,o,n){void 0===n&&(n=o),e[n]=t[o]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var o in e)"default"!==o&&Object.prototype.hasOwnProperty.call(e,o)&&n(t,e,o);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.deactivate=t.activate=void 0;const a=r(o(398)),c=o(435);t.activate=function(e){console.log("Blockly extension is now active!");const t=new c.BlocklyEditorProvider(e),o=a.window.registerCustomEditorProvider("blockly.editor",t,{webviewOptions:{retainContextWhenHidden:!0},supportsMultipleEditorsPerDocument:!1}),n=a.commands.registerCommand("blockly.newFile",(async()=>{const e=a.workspace.workspaceFolders?.[0];if(!e)return void a.window.showErrorMessage("Please open a workspace folder first.");const t=await a.window.showInputBox({prompt:"Enter the name for the new Blockly file",value:"untitled.blockly",validateInput:e=>e?e.endsWith(".blockly")||e.endsWith(".blocks")?null:"File must have .blockly or .blocks extension":"File name cannot be empty"});if(t){const o=a.Uri.joinPath(e.uri,t),n=JSON.stringify({blocks:{languageVersion:0,blocks:[]}},null,2);await a.workspace.fs.writeFile(o,Buffer.from(n,"utf8"));const i=await a.workspace.openTextDocument(o);await a.window.showTextDocument(i)}})),i=a.commands.registerCommand("blockly.exportCode",(()=>{const e=a.window.activeTextEditor;e&&e.document.fileName.endsWith(".blockly")&&a.window.showInformationMessage("Export code functionality will be implemented in the webview.")})),r=a.commands.registerCommand("blockly.clearWorkspace",(()=>{a.window.showInformationMessage("Clear workspace functionality will be implemented in the webview.")}));e.subscriptions.push(o,n,i,r)},t.deactivate=function(){console.log("Blockly extension is now deactivated.")}},398:e=>{e.exports=require("vscode")},435:function(e,t,o){var n=this&&this.__createBinding||(Object.create?function(e,t,o,n){void 0===n&&(n=o);var i=Object.getOwnPropertyDescriptor(t,o);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[o]}}),Object.defineProperty(e,n,i)}:function(e,t,o,n){void 0===n&&(n=o),e[n]=t[o]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var o in e)"default"!==o&&Object.prototype.hasOwnProperty.call(e,o)&&n(t,e,o);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.BlocklyEditorProvider=void 0;const a=r(o(398));class c{constructor(e){this.context=e}static register(e){const t=new c(e);return a.window.registerCustomEditorProvider(c.viewType,t)}async resolveCustomTextEditor(e,t,o){function n(){t.webview.postMessage({type:"update",content:e.getText()})}t.webview.options={enableScripts:!0},t.webview.html=this.getHtmlForWebview(t.webview);const i=a.workspace.onDidChangeTextDocument((t=>{t.document.uri.toString()===e.uri.toString()&&n()}));t.onDidDispose((()=>{i.dispose()})),t.webview.onDidReceiveMessage((t=>{switch(t.type){case"save":return void this.updateTextDocument(e,t.content);case"export":return void this.exportGeneratedCode(t.code,t.language);case"ready":return void n()}})),n()}getHtmlForWebview(e){const t=e.asWebviewUri(a.Uri.joinPath(this.context.extensionUri,"media","blocklyEditor.js")),o=e.asWebviewUri(a.Uri.joinPath(this.context.extensionUri,"media","blocklyEditor.css")),n=function(){let e="";const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";for(let o=0;o<32;o++)e+=t.charAt(Math.floor(62*Math.random()));return e}();return`\n            <!DOCTYPE html>\n            <html lang="en">\n            <head>\n                <meta charset="UTF-8">\n                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; img-src ${e.cspSource}; style-src ${e.cspSource} 'unsafe-inline'; script-src 'nonce-${n}';">\n                <meta name="viewport" content="width=device-width, initial-scale=1.0">\n                <link href="${o}" rel="stylesheet" />\n                <title>Blockly Editor</title>\n            </head>\n            <body>\n                <div id="toolbar">\n                    <button id="exportBtn">Export Code</button>\n                    <button id="clearBtn">Clear Workspace</button>\n                    <select id="languageSelect">\n                        <option value="JavaScript">JavaScript</option>\n                        <option value="Python">Python</option>\n                        <option value="Dart">Dart</option>\n                        <option value="Lua">Lua</option>\n                        <option value="PHP">PHP</option>\n                    </select>\n                </div>\n                <div id="container">\n                    <div id="blocklyDiv"></div>\n                    <div id="codeDiv">\n                        <h3>Generated Code:</h3>\n                        <pre id="codeOutput"></pre>\n                    </div>\n                </div>\n                <script src="https://unpkg.com/blockly/blockly.min.js" nonce="${n}"><\/script>\n                <script nonce="${n}" src="${t}"><\/script>\n            </body>\n            </html>`}updateTextDocument(e,t){const o=new a.WorkspaceEdit;return o.replace(e.uri,new a.Range(0,0,e.lineCount,0),t),a.workspace.applyEdit(o)}async exportGeneratedCode(e,t){const o=`generated_code.${this.getFileExtension(t)}`,n=a.workspace.workspaceFolders?.[0];if(!n)return void a.window.showErrorMessage("Please open a workspace folder first.");const i=a.Uri.joinPath(n.uri,o);await a.workspace.fs.writeFile(i,Buffer.from(e,"utf8"));const r=await a.workspace.openTextDocument(i);await a.window.showTextDocument(r)}getFileExtension(e){switch(e){case"JavaScript":return"js";case"Python":return"py";case"Dart":return"dart";case"Lua":return"lua";case"PHP":return"php";default:return"txt"}}}t.BlocklyEditorProvider=c,c.viewType="blockly.editor"}},t={},o=function o(n){var i=t[n];if(void 0!==i)return i.exports;var r=t[n]={exports:{}};return e[n].call(r.exports,r,r.exports,o),r.exports}(265);module.exports=o})();
//# sourceMappingURL=extension.js.map