/*! @azure/msal-common v15.6.0 2025-05-06 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * AuthErrorMessage class containing string constants used by error codes and messages.
 */
const unexpectedError = "unexpected_error";
const postRequestFailed = "post_request_failed";

export { postRequestFailed, unexpectedError };
//# sourceMappingURL=AuthErrorCodes.mjs.map
