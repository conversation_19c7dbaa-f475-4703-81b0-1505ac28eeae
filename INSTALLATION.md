# Installation Guide

## Quick Start

### Option 1: Development Mode (Recommended for Testing)

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd vsc-blockly
   npm install
   npm run compile
   ```

2. **Launch Extension Development Host**
   - Open the project in VS Code
   - Press `F5` or go to `Run > Start Debugging`
   - This will open a new VS Code window with the extension loaded

3. **Test the Extension**
   - In the new window, create a new file with `.blockly` extension
   - Or use Command Palette (`Ctrl+Shift+P`) and run "Blockly: New File"
   - The Blockly visual editor should open

### Option 2: Package and Install

1. **Build the Extension Package**
   ```bash
   npm install
   npm run compile
   npm run package
   ```

2. **Install the Extension**
   ```bash
   code --install-extension vsc-blockly-0.1.0.vsix
   ```

## Testing the Extension

### Create Your First Blockly Program

1. **Create a new Blockly file**
   - Command Palette: `Ctrl+Shift+P` → "Blockly: New File"
   - Or create a file with `.blockly` extension manually

2. **Build a simple program**
   - Drag a "print" block from the Text category
   - Add a text block with "Hello, World!"
   - Connect them together

3. **View generated code**
   - See the JavaScript code in the right panel
   - Change language using the dropdown

4. **Export code**
   - Click "Export Code" to save generated code to a file

### Sample Files

Try opening the sample files in the `samples/` directory:
- `hello-world.blockly` - Simple Hello World example
- `example.blockly` - More complex example with variables and loops

## Troubleshooting

### Extension Not Loading
- Make sure VS Code version is 1.74.0 or higher
- Check the Developer Console (`Help > Toggle Developer Tools`) for errors
- Ensure all dependencies are installed: `npm install`

### Blockly Editor Not Displaying
- Check if the file has `.blockly` or `.blocks` extension
- Verify the file contains valid JSON
- Try creating a new file using the command palette

### Code Generation Issues
- Ensure you've selected a supported language (JavaScript, Python, Dart, Lua, PHP)
- Check that blocks are properly connected
- Some blocks may require specific configurations

### Performance Issues
- Large workspaces may be slow - try breaking into smaller files
- Clear browser cache if using webview
- Restart VS Code if issues persist

## Development Setup

### Prerequisites
- Node.js 16.x or higher
- VS Code 1.74.0 or higher
- Git (for cloning repository)

### Build Commands
```bash
# Install dependencies
npm install

# Compile TypeScript
npm run compile

# Watch for changes (development)
npm run watch

# Package extension
npm run package

# Run tests
npm test
```

### Project Structure
```
vsc-blockly/
├── src/                  # TypeScript source files
├── media/               # Webview assets (HTML, CSS, JS)
├── samples/             # Example Blockly files
├── out/                 # Compiled JavaScript
├── package.json         # Extension manifest
└── README.md           # Documentation
```

## Support

If you encounter issues:
1. Check the [README.md](README.md) for detailed usage instructions
2. Look at the sample files for examples
3. Check VS Code's Developer Console for error messages
4. Create an issue in the project repository

## Next Steps

After installation:
1. Read the [README.md](README.md) for comprehensive usage guide
2. Try the sample files in the `samples/` directory
3. Explore different block categories and code generation languages
4. Create your own visual programs!
